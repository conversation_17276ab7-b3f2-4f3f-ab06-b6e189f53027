# -*- coding: utf-8 -*-
"""
Vchasno.py

Скачивает файлы из Вчасно.ua
https://edo.vchasno.ua/app/
Этот скрипт автоматизирует процесс авторизации, заполнения формы, скачивания файла,
а затем проходит по ссылкам в отчете и скачивает каждый документ в формате PDF.
Финальная, надежная версия, использующая обновление страницы (F5) для обхода всплывающих окон.

https://aistudio.google.com/prompts/1U8AIdxq9u_Bm-J-9wRGDGIxcC88tjrYj

"""

import os
import time
import re
from datetime import date, timedelta
from dotenv import load_dotenv
import pandas as pd

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# --- Глобальные константы и настройка ---
load_dotenv()

LOGIN_EMAIL = os.getenv("VCHASNO_LOGIN_EMAIL")
LOGIN_PASSWORD = os.getenv("VCHASNO_LOGIN_PASSWORD")

START_URL = "https://edo.vchasno.ua/auth/start"
LOGIN_URL = "https://edo.vchasno.ua/auth/login"
SUCCESS_URL_PART = "/app/"

DOWNLOAD_DIR = os.path.join(os.getcwd(), "downloads")

# --- Вспомогательные функции ---

def paste_from_clipboard_and_verify(driver, element, text):
    """Надежный метод ввода текста без использования буфера обмена."""
    print(f"    -> Вводим текст напрямую для '{text[:10]}...'")
    try:
        element.click()
        time.sleep(0.3)
        element.clear()
        time.sleep(0.3)
        element.send_keys(text)
        time.sleep(0.5)
    except Exception:
        driver.execute_script("arguments[0].click();", element)
        time.sleep(0.3)
        driver.execute_script("arguments[0].value = '';", element)
        time.sleep(0.3)
        element.send_keys(text)
        time.sleep(0.5)

    if element.get_attribute("value") != text:
        raise Exception(f"Не удалось ввести значение '{text[:10]}...'.")
    print(" -> Значение успешно введено.")

def human_like_type(element, text):
    """Имитирует человеческий ввод: клик, очистка, печать по одному символу."""
    print(f"    -> Имитируем человеческий ввод для значения '{text}'...")
    try:
        element.click()
        time.sleep(0.3)
        element.clear()
        time.sleep(0.3)
        for char in text:
            element.send_keys(char)
            time.sleep(0.05)
        print(" -> Значение успешно напечатано.")
    except Exception as e:
        raise Exception(f"Ошибка при 'человеческом' вводе: {e}")

def sanitize_filename(filename):
    """Очищает имя файла от недопустимых символов."""
    return re.sub(r'[\\/*?:"<>|]', "_", str(filename))

def configure_driver():
    """Настраивает и возвращает экземпляр Chrome WebDriver."""
    print("Конфигурируем браузер для АВТОМАТИЧЕСКОГО скачивания...")
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    
    chrome_options = Options()
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    prefs = {
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "download.default_directory": DOWNLOAD_DIR,
        "safebrowsing.enabled": True,
        "credentials_enable_service": False,
        "profile.password_manager_enabled": False,
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

# --- Основная логика скрипта ---

def main():
    """Главная функция, выполняющая весь процесс скачивания."""
    driver = configure_driver()
    wait = WebDriverWait(driver, 20)
    
    try:
        print("--- Запуск скрипта (финальная версия) ---")

        # --- ЭТАПЫ АВТОРИЗАЦИИ (Шаги 1-5) ---
        print(f"\nШаг 1: Открываем стартовую страницу {START_URL}...")
        driver.get(START_URL)
        print("Шаг 2: Вводим Email...")
        email_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login input")))
        paste_from_clipboard_and_verify(driver, email_input, LOGIN_EMAIL)
        continue_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login > div > div > button")))
        driver.execute_script("arguments[0].click();", continue_button)
        print(" -> Нажата кнопка 'Продовжити'.")
        wait.until(EC.url_to_be(LOGIN_URL))
        print("\nШаг 4: Вводим Пароль...")
        password_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#password")))
        paste_from_clipboard_and_verify(driver, password_input, LOGIN_PASSWORD)
        submit_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']")))
        driver.execute_script("arguments[0].click();", submit_button)
        wait.until(EC.url_contains(SUCCESS_URL_PART))
        print("\n[V] УСПЕХ! Авторизация прошла успешно.")
        
        # --- СКАЧИВАНИЕ CSV ОТЧЕТА (Шаги 6-9) ---
        print("\nШаг 6: Ищем и нажимаем на кнопку экспорта...")
        button_xpath = "//*[@id='content']/div/div[2]/div[2]/div/div[3]/div/div[1]/button"
        target_button = wait.until(EC.element_to_be_clickable((By.XPATH, button_xpath)))
        driver.execute_script("arguments[0].click();", target_button)
        print("\nШаг 7: Заполняем форму экспорта...")
        start_date_obj = date.today() - timedelta(days=364)
        end_date_obj = date.today()
        start_date_str = start_date_obj.strftime('%d.%m.%Y')
        end_date_str = end_date_obj.strftime('%d.%m.%Y')
        start_date_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.exportDataPopup__datepickersBlock__eGkgQ > label:nth-child(1) input")))
        human_like_type(start_date_input, start_date_str)
        start_date_input.send_keys(Keys.ENTER)
        end_date_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.exportDataPopup__datepickersBlock__eGkgQ > label:nth-child(3) input")))
        human_like_type(end_date_input, end_date_str)
        end_date_input.send_keys(Keys.ENTER)
        
        print("\nШаг 8: Ожидание автоматического скачивания файла...")
        files_before = set(os.listdir(DOWNLOAD_DIR))
        export_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "div.exportDataPopup__buttonBlock__kixdI > button")))
        export_button.click()
        print(" -> Нажата кнопка 'Експортувати'. Ожидаем CSV файл...")

        max_wait_time = 90
        wait_start_time = time.time()
        downloaded_filename = None
        while time.time() - wait_start_time < max_wait_time:
            files_after = set(os.listdir(DOWNLOAD_DIR))
            new_files = files_after - files_before
            if new_files:
                for filename in new_files:
                    if not filename.endswith('.crdownload'):
                        downloaded_filename = filename
                        break
            if downloaded_filename:
                break
            time.sleep(1)

        if not downloaded_filename:
            raise Exception(f"CSV файл не был скачан в течение {max_wait_time} секунд.")

        print("\nШаг 9: Переименование скачанного CSV файла...")
        downloaded_file_path = os.path.join(DOWNLOAD_DIR, downloaded_filename)
        start_date_filename = start_date_obj.strftime('%Y-%m-%d')
        end_date_filename = end_date_obj.strftime('%Y-%m-%d')
        final_filename = f"vhcasno_{start_date_filename}_{end_date_filename}.csv"
        final_file_path = os.path.join(DOWNLOAD_DIR, final_filename)
        if os.path.exists(final_file_path):
            os.remove(final_file_path)
        os.rename(downloaded_file_path, final_file_path)
        print(f" -> Файл успешно переименован в '{final_filename}'")

        # --- ЧТЕНИЕ CSV И ЗАКРЫТИЕ МОДАЛЬНОГО ОКНА (Шаги 10-11) ---
        print("\nШаг 10: Загружаем данные в pandas DataFrame...")
        df = pd.read_csv(final_file_path, sep=';', on_bad_lines='skip', engine='python')
        print(" -> Данные из CSV успешно загружены.")
        
        print("\nШаг 11: Закрываем диалоговое окно экспорта...")
        # Используем Selenium для отправки клавиши Escape
        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
        print(" -> Клавиша Escape отправлена, окно должно быть закрыто.")
        time.sleep(2)

        # --- СКАЧИВАНИЕ PDF ДОКУМЕНТОВ (Шаг 12) ---
        print("\nШаг 12: Начинаем скачивание PDF документов по ссылкам...")
        pdf_download_dir = os.path.join(DOWNLOAD_DIR, "pdf")
        os.makedirs(pdf_download_dir, exist_ok=True)
        
        if df.empty:
            print(" -> DataFrame пуст, скачивать нечего.")
        else:
            # Ограничим количество документов для тестирования
            total_docs = len(df)
            print(f" -> Всего документов для скачивания: {total_docs}")

            for i, (_, row) in enumerate(df.iterrows()):
                # Скачиваем только первые 3 документа для тестирования
                if i >= 3:
                    print(f" -> Остановка после {i} документов для анализа результатов")
                    break

                doc_url = row.get('Посилання на документ')
                doc_number = sanitize_filename(row.get('Номер документа', f"doc_{i+1}"))
                final_pdf_path = os.path.join(pdf_download_dir, f"{doc_number}.pdf")

                if not isinstance(doc_url, str) or not doc_url.startswith('http'):
                    print(f" [!] Пропускаем строку {i+1}: неверная ссылка '{doc_url}'")
                    continue

                print(f"\n=== ДОКУМЕНТ {i+1}/{min(3, total_docs)}: '{doc_number}' ===")

                if os.path.exists(final_pdf_path):
                    print(f" -> Файл '{doc_number}.pdf' уже существует. Пропускаем.")
                    continue
                
                try:
                    print(f" -> Переходим по ссылке: ...{doc_url[-36:]}")
                    driver.get(doc_url)
                    
                    # =================================================================
                    # ИЗМЕНЕНИЕ: Обновляем страницу для устранения всплывающих окон
                    # =================================================================
                    print(" -> Обновляем страницу (F5) для очистки интерфейса...")
                    driver.refresh()
                    # Ждем, пока страница полностью прогрузится после обновления
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.document__content__bs1ZT")))
                    print(" -> Страница обновлена и готова к работе.")
                    time.sleep(2)  # Дополнительная пауза для полной загрузки
                    # =================================================================

                    # Попробуем найти родительскую кнопку вместо SVG
                    download_menu_button_selector = "div.documentHeader__cellTools__WCXBK > div > div:nth-child(4)"

                    print(" -> Ищем кнопку меню скачивания...")
                    download_menu = None

                    # Попробуем несколько вариантов селекторов
                    selectors_to_try = [
                        download_menu_button_selector,
                        "div.documentHeader__cellTools__WCXBK div:nth-child(4)",
                        "div.documentHeader__cellTools__WCXBK button",
                        "[class*='download']",
                        "[class*='menu']"
                    ]

                    for selector in selectors_to_try:
                        try:
                            download_menu = driver.find_element(By.CSS_SELECTOR, selector)
                            if download_menu:
                                print(f" -> Кнопка найдена по селектору: {selector}")
                                break
                        except:
                            continue

                    if not download_menu:
                        # Попробуем найти любую кнопку скачивания
                        download_buttons = driver.find_elements(By.XPATH, "//button | //div[@role='button'] | //*[contains(@class, 'button')] | //*[contains(text(), 'Скачать')] | //*[contains(text(), 'Download')]")
                        if download_buttons:
                            print(f" -> Найдено {len(download_buttons)} потенциальных кнопок, пробуем...")
                            for btn in download_buttons[:3]:  # Попробуем первые 3
                                try:
                                    btn.click()
                                    download_menu = btn
                                    print(" -> Успешно кликнули на кнопку")
                                    break
                                except:
                                    continue

                    if download_menu:
                        try:
                            # Попробуем обычный клик
                            download_menu.click()
                            time.sleep(1)
                            print(" -> Кликнули на кнопку меню (обычный клик)")
                        except:
                            try:
                                # Попробуем через ActionChains
                                from selenium.webdriver.common.action_chains import ActionChains
                                actions = ActionChains(driver)
                                actions.move_to_element(download_menu).click().perform()
                                time.sleep(1)
                                print(" -> Кликнули через ActionChains")
                            except:
                                try:
                                    # Попробуем через JavaScript
                                    driver.execute_script("arguments[0].click();", download_menu)
                                    time.sleep(1)
                                    print(" -> Кликнули через JavaScript")
                                except Exception as e:
                                    print(f" -> Не удалось кликнуть: {e}")
                                    continue
                    else:
                        print(" -> Кнопка меню не найдена")
                        continue

                    print(" -> Ищем опцию 'Завантажити PDF' в выпадающем списке...")
                    files_before_pdf = set(os.listdir(DOWNLOAD_DIR))
                    time.sleep(1)

                    # Ищем элемент с текстом "Завантажити PDF"
                    pdf_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Завантажити PDF')] | //*[contains(text(), 'PDF')] | //*[contains(text(), 'Скачать PDF')]")
                    if pdf_elements:
                        print(f" -> Найдено {len(pdf_elements)} элементов с PDF, кликаем на первый...")
                        driver.execute_script("arguments[0].click();", pdf_elements[0])
                        print(" -> Кликнули на опцию 'Завантажити PDF'")
                    else:
                        print(" -> Элементы с 'Завантажити PDF' не найдены, пробуем другие варианты...")
                        # Попробуем найти любые элементы меню
                        menu_items = driver.find_elements(By.XPATH, "//li | //div[@role='menuitem'] | //a | //button | //span")
                        print(f" -> Найдено {len(menu_items)} элементов меню")
                        found_pdf = False
                        for item in menu_items[:10]:  # Проверим первые 10
                            try:
                                text = item.text.lower()
                                print(f" -> Элемент меню: '{text}'")
                                if 'завантажити' in text and 'pdf' in text:
                                    print(" -> Найден элемент 'Завантажити PDF', кликаем...")
                                    driver.execute_script("arguments[0].click();", item)
                                    found_pdf = True
                                    break
                                elif 'pdf' in text and ('скачать' in text or 'download' in text):
                                    print(" -> Найден элемент с PDF для скачивания, кликаем...")
                                    driver.execute_script("arguments[0].click();", item)
                                    found_pdf = True
                                    break
                            except:
                                continue
                        if not found_pdf:
                            print(" -> Опция 'Завантажити PDF' не найдена в меню")

                    print(" -> Ожидаем скачивание PDF файла...")
                    pdf_wait_start = time.time()
                    downloaded_pdf = None
                    wait_time = 30  # Уменьшим время ожидания

                    while time.time() - pdf_wait_start < wait_time:
                        files_after_pdf = set(os.listdir(DOWNLOAD_DIR))
                        new_pdf_files = files_after_pdf - files_before_pdf
                        if new_pdf_files:
                            print(f" -> Обнаружены новые файлы: {new_pdf_files}")
                            for f in new_pdf_files:
                                if not f.endswith('.crdownload'):
                                    downloaded_pdf = f
                                    print(f" -> Найден скачанный файл: {f}")
                                    break
                        if downloaded_pdf:
                            break
                        time.sleep(1)

                    if not downloaded_pdf:
                        print(f" -> PDF файл не был скачан за {wait_time} секунд")
                        print(f" -> Файлы до: {files_before_pdf}")
                        print(f" -> Файлы после: {set(os.listdir(DOWNLOAD_DIR))}")
                        print(" -> РЕЗУЛЬТАТ: НЕУДАЧА - файл не скачался")

                        # Проверим папку PDF
                        pdf_files = os.listdir(pdf_download_dir)
                        print(f" -> PDF файлы в папке: {pdf_files}")
                        continue  # Переходим к следующему документу

                    print(f" -> Файл '{downloaded_pdf}' скачан. Переименовываем...")
                    os.rename(os.path.join(DOWNLOAD_DIR, downloaded_pdf), final_pdf_path)
                    print(f" [V] РЕЗУЛЬТАТ: УСПЕХ - файл сохранен как '{doc_number}.pdf'")

                    # Проверим, что файл действительно создался
                    if os.path.exists(final_pdf_path):
                        file_size = os.path.getsize(final_pdf_path)
                        print(f" -> Подтверждение: файл существует, размер {file_size} байт")
                    else:
                        print(f" -> ОШИБКА: файл не найден по пути {final_pdf_path}")

                except Exception as pdf_e:
                    print(f" [X] ОШИБКА при скачивании документа '{doc_number}': {pdf_e}")
                    print(" -> РЕЗУЛЬТАТ: ОШИБКА - исключение в процессе скачивания")

                # Пауза между документами для анализа
                print(f"\n--- ПАУЗА 3 секунды перед следующим документом ---")
                time.sleep(3)

        print("\n[V] Скрипт успешно завершен. Браузер останется открытым.")

    except Exception as e:
        print(f"\n[X] ГЛОБАЛЬНАЯ ОШИБКА: {e}")
        print(" -> Браузер останется открытым для анализа ошибки.")
    
    finally:
        input("\nНажмите Enter в этой консоли, чтобы закрыть браузер...")
        if 'driver' in locals() and driver:
            driver.quit()
            print("Браузер закрыт.")


# --- Точка входа в программу ---
if __name__ == "__main__":
    main()