# извлечение данных из документов с помощью Google Document AI (Async версия)
# Установите Google Cloud SDK !!!
# в cmd выполнить gcloud auth application-default login, выбрать project_id и выставить 622152118679
# в cmd выполнить gcloud config set project 622152118679
# или выбрать другой проект из https://console.cloud.google.com/projectselector2/home/<USER>
# pip install --upgrade google-cloud-documentai
# pip install aiofiles

import aiofiles
from typing import Optional, Union
from google.api_core.client_options import ClientOptions
from google.cloud import documentai_v1beta3 as documentai
from google.auth import default
from dotenv import load_dotenv
import os
import sys
import asyncio

load_dotenv()

# Проверяем аутентификацию
try:
    credentials, project = default()
    # print(f"Успешная аутентификация. Используется проект по умолчанию: {project}")
except Exception as e:
    print(f"Ошибка аутентификации: {str(e)}")
    # print("Пожалуйста, выполните команду 'gcloud auth application-default login'")
    sys.exit(1)

# Параметры проекта
project_id = os.getenv("GOOGLE_PROJECT_NUMBER")
location = os.getenv("GOOGLE_PROJECT_LOCATION")
processor_id = os.getenv("GOOGLE_OCR_PROCESSOR_ID")
processor_name = os.getenv("GOOGLE_OCR_PROCESSOR_NAME")


async def google_ocr_documentai_async(
    file_path: str,
    page_number: Union[int, list[int]] = [1],
    mime_type: Optional[str] = "image/png",
    field_mask: Optional[str] = None,
    processor_version_id: Optional[str] = None,
) -> Optional[str]:
    """
    Асинхронная обработка документа с помощью Google Document AI.
    
    Args:
        file_path: Путь к файлу
        page_number: Номер страницы или список номеров страниц
        mime_type: MIME тип файла
        field_mask: Маска полей для возврата
        processor_version_id: ID версии процессора
        
    Returns:
        Извлеченный текст или None в случае ошибки
    """
    
    async def _process_document():
        try:
            # Создаем клиент Document AI
            opts = ClientOptions(api_endpoint=f"{location}-documentai.googleapis.com")
            client = documentai.DocumentProcessorServiceClient(client_options=opts)

            # Определяем путь к процессору
            if processor_version_id:
                name = client.processor_version_path(
                    project_id, location, processor_id, processor_version_id
                )
            else:
                name = client.processor_path(project_id, location, processor_id)

            # Проверяем существование файла
            if not await asyncio.to_thread(os.path.exists, file_path):
                print(f"Ошибка: Файл не найден: {file_path}")
                return None

            # Асинхронно читаем файл
            async with aiofiles.open(file_path, "rb") as file:
                image_content = await file.read()

            print(f"Файл успешно прочитан, размер: {len(image_content)} байт")

            # Подготавливаем документ
            raw_document = documentai.RawDocument(content=image_content, mime_type=mime_type)

            # Настраиваем опции обработки
            pages_to_process = page_number if isinstance(page_number, list) else [page_number]
            process_options = documentai.ProcessOptions(
                individual_page_selector=documentai.ProcessOptions.IndividualPageSelector(
                    pages=pages_to_process
                )
            )

            # Создаем запрос
            request = documentai.ProcessRequest(
                name=name,
                raw_document=raw_document,
                field_mask=field_mask,
                process_options=process_options,
            )

            # Выполняем обработку в отдельном потоке (т.к. это синхронный вызов)
            result = await asyncio.to_thread(client.process_document, request)
            
            return result.document.text

        except Exception as e:
            print(f"Произошла ошибка при обработке документа: {str(e)}")
            return None

    return await _process_document()


async def google_ocr_documentai(
    file_path: str,
    page_number: Union[int, list[int]] = [1],
    mime_type: Optional[str] = "image/png",
    field_mask: Optional[str] = None,
    processor_version_id: Optional[str] = None,
) -> Optional[str]:
    """
    Синхронная обертка для обратной совместимости.
    Рекомендуется использовать google_ocr_documentai_async.
    """
    return await google_ocr_documentai_async(
        file_path, page_number, mime_type, field_mask, processor_version_id
    )


async def batch_process_documents(
    file_paths: list[str],
    page_numbers: Optional[list[Union[int, list[int]]]] = None,
    mime_type: str = "image/png",
    max_concurrent: int = 5
) -> list[Optional[str]]:
    """
    Асинхронная пакетная обработка множества документов.
    
    Args:
        file_paths: Список путей к файлам
        page_numbers: Список номеров страниц для каждого файла
        mime_type: MIME тип файлов
        max_concurrent: Максимальное количество одновременных запросов
        
    Returns:
        Список результатов обработки
    """
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_with_semaphore(file_path: str, page_num: Union[int, list[int]] = [1]):
        async with semaphore:
            return await google_ocr_documentai_async(
                file_path=file_path,
                page_number=page_num,
                mime_type=mime_type
            )
    
    # Подготавливаем задачи
    tasks = []
    for i, file_path in enumerate(file_paths):
        page_num = page_numbers[i] if page_numbers and i < len(page_numbers) else [1]
        tasks.append(process_with_semaphore(file_path, page_num))
    
    # Выполняем все задачи параллельно
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Обрабатываем результаты и ошибки
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"Ошибка при обработке файла {file_paths[i]}: {result}")
            processed_results.append(None)
        else:
            processed_results.append(result)
    
    return processed_results


async def process_pdf_pages_async(
    pdf_path: str,
    pages: Optional[list[int]] = None,
    max_concurrent: int = 3
) -> dict[int, Optional[str]]:
    """
    Асинхронная обработка нескольких страниц PDF файла.
    
    Args:
        pdf_path: Путь к PDF файлу
        pages: Список номеров страниц для обработки (если None - все страницы)
        max_concurrent: Максимальное количество одновременных запросов
        
    Returns:
        Словарь {номер_страницы: текст}
    """
    if pages is None:
        # Определяем количество страниц в PDF
        import fitz
        doc = await asyncio.to_thread(fitz.open, pdf_path)
        page_count = len(doc)
        doc.close()
        pages = list(range(1, page_count + 1))
    
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_page_with_semaphore(page_num: int):
        async with semaphore:
            result = await google_ocr_documentai_async(
                file_path=pdf_path,
                page_number=[page_num],
                mime_type="application/pdf"
            )
            return page_num, result
    
    # Создаем задачи для всех страниц
    tasks = [process_page_with_semaphore(page_num) for page_num in pages]
    
    # Выполняем все задачи параллельно
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Собираем результаты в словарь
    page_texts = {}
    for result in results:
        if isinstance(result, Exception):
            print(f"Ошибка при обработке страницы: {result}")
            continue
        
        page_num, text = result
        page_texts[page_num] = text
    
    return page_texts


async def main():
    """Пример использования асинхронных функций"""
    
    # Пример 1: Обработка одного файла
    print("=== Обработка одного файла ===")
    result = await google_ocr_documentai_async(
        file_path=r"c:\Scan\All\TestVN2str.pdf",
        page_number=[1, 2],
        mime_type="application/pdf",
    )
    print("Результат:", result[:200] if result else "Ошибка")
    
    # Пример 2: Пакетная обработка нескольких файлов
    print("\n=== Пакетная обработка ===")
    file_paths = [
        r"c:\Scan\All\TestVN2str.pdf",
        # Добавьте другие файлы
    ]
    page_numbers = [[1], [2]]  #