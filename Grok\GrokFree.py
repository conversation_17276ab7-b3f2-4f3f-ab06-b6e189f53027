# pip install groq python-dotenv
# https://console.groq.com/playground?model=qwen-qwq-32b

import os
from groq import Groq
from dotenv import load_dotenv
from prompt import GEMINI_AI_PROMPT

# Загрузка переменных окружения из файла .env
load_dotenv()

# Получаем API ключ из переменной окружения GROK_API_KEY
api_key = os.getenv("GROK_API_KEY_FREE")
if not api_key:
    print("Ошибка: Не найден API ключ GROK_API_KEY в файле .env")
    exit(1)

# Создаем клиент Groq с API ключом
client = Groq(api_key=api_key)

content = """
Eyeball Marshmallow 4gx100pcsx12boxes'Eyeball Marshmallow 4gx100pcsx12boxes',
Jelly cup in Elephant Jar  13gx100pcsx6jars'Jelly cup in Elephant Jar  13gx100pcsx6jars',
Jelly cup in bear Jar  13gx100pcsx6jars'Jelly cup in bear Jar  13gx100pcsx6jars',
Jelly cup in Dragon Jar  13gx100pcsx6jars'Jelly cup in Dragon Jar  13gx100pcsx6jars',
Jelly cup in panda Jar  13gx100pcsx6jars'Jelly cup in panda Jar  13gx100pcsx6jars',
Mini cup Jelly(Cow Jar) 13gx100pcsx6jars'Mini cup Jelly(Cow Jar) 13gx100pcsx6jars',
Mini Pudding (Mashroom Jar) 13gx100pcsx6jars'Mini Pudding (Mashroom Jar) 13gx100pcsx6jars',
Mini pudding (Dog Jar) 13gx100pcsx6jars'Mini pudding (Dog Jar) 13gx100pcsx6jars',
Mini Cup Jelly (Panda Bag) 13gx100pcsx6jars'Mini Cup Jelly (Panda Bag) 13gx100pcsx6jars',
Mini Pudding(KT Cat Jar) 13gx100pcsx6jars'Mini Pudding(KT Cat Jar) 13gx100pcsx6jars',
Mini Pudding(MK Mouse Jar) 13gx100pcsx6jars'Mini Pudding(MK Mouse Jar) 13gx100pcsx6jars',
Mini Jelly cup in (Trush can)  13gx100pcsx6jars'Mini Jelly cup in (Trush can)  13gx100pcsx6jars',
Mini cup Jelly (Hippo Jar) 13gx100pcsx6jars'Mini cup Jelly (Hippo Jar) 13gx100pcsx6jars',
Mini Pudding(Dinosaur Jar) 13gx100pcsx6jars'Mini Pudding(Dinosaur Jar) 13gx100pcsx6jars',
Mini Pudding(Frog Jar) 13gx100pcsx6jars'Mini Pudding(Frog Jar) 13gx100pcsx6jars',
Cola/Orange/Lemon Spray Candy 25mlx24pcsx12boxes'Cola/Orange/Lemon Spray Candy 25mlx24pcsx12boxes',
Mini Extinguisher Spray Candy 25ml*30pcs*12boxes'Mini Extinguisher Spray Candy 25ml*30pcs*12boxes',
Melody pop Whistle Candy 10xg30pcsx12boxes'Melody pop Whistle Candy 10xg30pcsx12boxes',
Choco Punto 10gx24pcsx24boxes'Choco Punto 10gx24pcsx24boxes',
Baby bottle(Whistle candy)  9gx100pcsx6jars'Baby bottle(Whistle candy)  9gx100pcsx6jars',
Doughnut Pudding and pin Candy 14gx30pcsx20boxes'Doughnut Pudding and pin Candy 14gx30pcsx20boxes',
Grape Pudding 68g x 12pcs x 12box'Grape Pudding 68g x 12pcs x 12box',
Toy and Jelly 30gx50pcsx6jars'Toy and Jelly 30gx50pcsx6jars',
Snake jelly 35gx48pcsx6jars'Snake jelly 35gx48pcsx6jars',
Crocodile jelly 35gx48pcsx6jars'Crocodile jelly 35gx48pcsx6jars',
2in1 Crazy Hair Candy 20gx20pcsx12boxes'2in1 Crazy Hair Candy 20gx20pcsx12boxes',
Macaron BISCO (Mixed Flavors) 80gx36boxes'Macaron BISCO (Mixed Flavors) 80gx36boxes',
5121-1Gong Animals Toys&bubble&candy 3GX12pcsX6boxes'5121-1Gong Animals Toys&bubble&candy 3GX12pcsX6boxes',
5131-1Hands and FeetsToys&bubble&Candy  3GX12pcsX6boxes'5131-1Hands and FeetsToys&bubble&Candy  3GX12pcsX6boxes',
Bear Jelly 35gx30pcsx12boxes'Bear Jelly 35gx30pcsx12boxes',
Milk Bottle Candy 3in1 16gx24x10boxes'Milk Bottle Candy 3in1 16gx24x10boxes',
Mini Lollipop Shooter 3gx12pcsx 24boxes'Mini Lollipop Shooter 3gx12pcsx 24boxes',
Mega sour jam 30gx20pcsx12boxes'Mega sour jam 30gx20pcsx12boxes',
Bracelet Candy 10gx48pcsx12boxes UNI'Bracelet Candy 10gx48pcsx12boxes UNI',
Colour Candy Ball Stick 12gx40pcsx8vases'Colour Candy Ball Stick 12gx40pcsx8vases',
Skateboard Crazy Hair candy 20gx20pcsx12boxes'Skateboard Crazy Hair candy 20gx20pcsx12boxes',
500g Macaron Cookies (Mixed Flavors) 500Gx 8Boxes'500g Macaron Cookies (Mixed Flavors) 500Gx 8Boxes',
Jelly Stick 10gx100pcsx12jars'Jelly Stick 10gx100pcsx12jars',
Eyeball Gummy Candy 2gx200pcsx12jars'Eyeball Gummy Candy 2gx200pcsx12jars',
Small Pop 40gx12pcsx4trays'Small Pop 40gx12pcsx4trays',
3D Eyeball Gummy 18gx50pcsx6jars'3D Eyeball Gummy 18gx50pcsx6jars',
4 Seesions Pudding 80gx24pcsx8jars'4 Seesions Pudding 80gx24pcsx8jars',
4D Gummy Candy 13gx30pcsx20boxes strw'4D Gummy Candy 13gx30pcsx20boxes strw',
Animal World Jelly 35gx70pcsx6jars'Animal World Jelly 35gx70pcsx6jars',
Crazy Popping Candy 10gx12pcsx12trays'Crazy Popping Candy 10gx12pcsx12trays',
Fish Jelly Candy+Popping Candy 23g×30pcs×16trays'Fish Jelly Candy+Popping Candy 23g×30pcs×16trays',
Hum Egg Jelly Popping 23g×30pcs×16trays'Hum Egg Jelly Popping 23g×30pcs×16trays',
Fruity Jelly Stick 12gx30pcsx12 jars'Fruity Jelly Stick 12gx30pcsx12 jars',
Heart Spray Candy 20mlx30pcsx12boxes'Heart Spray Candy 20mlx30pcsx12boxes',
Nipple Compressed candy 4gx50pcsx8jars'Nipple Compressed candy 4gx50pcsx8jars',
Round Cuppy Jelly 75gx6pcsx24trays'Round Cuppy Jelly 75gx6pcsx24trays',
Soda jelly 28gx48pcsx12boxes'Soda jelly 28gx48pcsx12boxes',
Stick Hamburger Gummy 18g*20pcs*12boxes'Stick Hamburger Gummy 18g*20pcs*12boxes',
Stick Macaron Gummy 18g*20pcs*12boxes'Stick Macaron Gummy 18g*20pcs*12boxes',
Mini Boom Spray Candy 30ml*20pcs*12boxes'Mini Boom Spray Candy 30ml*20pcs*12boxes',
Marshmallow (Twist) 20gx24pcsx12boxes'Marshmallow (Twist) 20gx24pcsx12boxes',
Marshmallow (emoji) 20gx24pcsx12boxes'Marshmallow (emoji) 20gx24pcsx12boxes',
New Mini cola Spray Candy 20mlx24pcsx24boxes'New Mini cola Spray Candy 20mlx24pcsx24boxes',
Twins Rose Jelly 38gx30pcsx12boxes'Twins Rose Jelly 38gx30pcsx12boxes',
My boy Jam (minions) 18gx20pcsx12boxes'My boy Jam (minions) 18gx20pcsx12boxes',
Frizzlers powder Candy 10gx30pcsx20boxes'Frizzlers powder Candy 10gx30pcsx20boxes',
Mouse Candy 6gx30pcsx24trays'Mouse Candy 6gx30pcsx24trays',
Lipstick Candy 6gx30pcsx20boxes'Lipstick Candy 6gx30pcsx20boxes',
Skull Jam 20gx30pcxs20boxes'Skull Jam 20gx30pcxs20boxes',
Mini Bomb Jelly 35gx55pcsx 6jars'Mini Bomb Jelly 35gx55pcsx 6jars',
Banana Gummy 8gx30pcsx20jars'Banana Gummy 8gx30pcsx20jars',
Cock Gummy 8gx30pcsx20jars'Cock Gummy 8gx30pcsx20jars',
Emoji gummy 8gx50pcsx12jars'Emoji gummy 8gx50pcsx12jars',
Turtle gummy 8gx50pcsx12jars'Turtle gummy 8gx50pcsx12jars',
Billard Gummy Candy 8g*50pcs*12jars'Billard Gummy Candy 8g*50pcs*12jars',
kokolin draje şek.kaplm.12*24*18gr choco punto'kokolin draje şek.kaplm.12*24*18gr choco punto',
ELLO POLONYA SEKERI STAND 25GX150'ELLO POLONYA SEKERI STAND 25GX150',
LOLLIPOP MAXXI SOUR CHERRY 10X48X28G'LOLLIPOP MAXXI SOUR CHERRY 10X48X28G',
LOLLIPOP MAXXI WATERMELON 10X48X28G'LOLLIPOP MAXXI WATERMELON 10X48X28G',
Blong Energy - Lollipop 28g 12x672g(24pcs)'Blong Energy - Lollipop 28g 12x672g(24pcs)',
BlongEnergy Mix - Lollipop 28g 12x672g(24pcs)'BlongEnergy Mix - Lollipop 28g 12x672g(24pcs)',
Blong Sour - Lollipop 28g 12x672g(24pcs)'Blong Sour - Lollipop 28g 12x672g(24pcs)',
"""
prompt = """
        Максимально точно определи и извлеки данные по каждому артикулу/sku.
        Выведи в формате СТРОГО ВАЛИДНОГО JSON. 
        поля заполни.
        {articles:[{
            "sku": str,  // наименование, которое тебе дал. Без изменений
            "grams_in_pcs": float,  // grams, ml
            "pcs_in_block": float,
            "box_in_cartoon": int,
            "weight_unit": float, // g, ml, kg, гр, кг, мл
            "pcs_type": str, // pcs, шт
            "box_type": str // jar, box, банка, блок
        }]}
        """

# Создаем запрос к API
completion = client.chat.completions.create(
    model="qwen-qwq-32b",
    messages=[
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
        {"role": "user", "content": """ты вывел данные по каждому артикулу/sku?. Из там много."""}
    ],
    temperature=0.2,
    max_completion_tokens=65000,
    top_p=0.95,
    stream=True,
    stop=None,
)

print("Ответ:")
# Выводим ответ по мере поступления
for chunk in completion:
    print(chunk.choices[0].delta.content or "", end="")
