# pip install google-cloud-documentai-toolbox

from google.cloud.documentai_toolbox import document

# TODO(developer): Uncomment these variables before running the sample.
# Given a local document.proto or sharded document.proto from a splitter/classifier in path
# document_path = "path/to/local/document.json"
# pdf_path = "path/to/local/document.pdf"
# output_path = "resources/output/"


def split_pdf_sample(document_path: str, pdf_path: str, output_path: str) -> None:
    wrapped_document = document.Document.from_document_path(document_path=document_path)

    output_files = wrapped_document.split_pdf(
        pdf_path=pdf_path, output_path=output_path
    )

    print("Document Successfully Split")
    for output_file in output_files:
        print(output_file)


if __name__ == "__main__":
    
    split_pdf_sample(
        r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\TTN.json",
        r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ТТН test.pdf",
        r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\output",
    )
