# pip install pytesseract
# pip install opencv-python
import pytesseract
from pdf2image import convert_from_path
import cv2
import numpy as np
import os
import platform
import re
import subprocess
import requests
import zipfile
import shutil
from pathlib import Path

"""
--oem (Режим движка OCR)
Этот параметр определяет, какой движок OCR будет использоваться для распознавания текста. Возможные значения:

0: Только устаревший движок.
1: Только движок на основе LSTM (Long Short-Term Memory).
2: Устаревший движок + движок на основе LSTM.
3: По умолчанию, автоматически выбирает лучший доступный движок.
Использование --oem 3 позволяет Tesseract автоматически выбирать лучший доступный движок, что обычно дает наилучшие результаты.

--psm (Режим сегментации страницы)
Этот параметр определяет, как Tesseract будет сегментировать страницу на блоки текста. Возможные значения:

0: Только определение ориентации и скрипта.
1: Автоматическая сегментация страницы с определением ориентации и скрипта (OSD).
2: Автоматическая сегментация страницы, но без OSD или OCR.
3: Полностью автоматическая сегментация страницы, но без OSD.
4: Предполагается один столбец текста переменного размера.
5: Предполагается один равномерный блок вертикально выровненного текста.
6: Предполагается один равномерный блок текста.
7: Рассматривать изображение как одну строку текста.
8: Рассматривать изображение как одно слово.
9: Рассматривать изображение как одно слово в круге.
10: Рассматривать изображение как один символ.
11: Разреженный текст. Найти как можно больше текста в произвольном порядке.
12: Разреженный текст с OSD.
13: Сырая строка. Рассматривать изображение как одну строку текста, обходя хаки, специфичные для Tesseract.
"""

def get_tessdata_dir():
    """Получить путь к директории tessdata"""
    os_name = platform.system()
    if os_name == 'Windows':
        tessdata_dir = os.getenv('TESSDATA_PREFIX', 'C:\\Program Files\\Tesseract-OCR\\tessdata')
    elif os_name == 'Linux':
        tessdata_dir = os.getenv('TESSDATA_PREFIX', '/usr/share/tesseract-ocr/tessdata')
    elif os_name == 'Darwin':  # macOS
        tessdata_dir = os.getenv('TESSDATA_PREFIX', '/usr/local/share/tessdata')
    else:
        raise OSError(f"Unsupported operating system: {os_name}")
    return tessdata_dir


def download_language_file(lang, tessdata_dir):
    """Скачать файл языка для Tesseract"""
    try:
        # URL для скачивания файлов языков Tesseract
        base_url = "https://github.com/tesseract-ocr/tessdata/raw/main"
        lang_file = f"{lang}.traineddata"
        url = f"{base_url}/{lang_file}"

        print(f"Скачивание языкового файла {lang_file}...")

        response = requests.get(url, stream=True)
        response.raise_for_status()

        lang_path = os.path.join(tessdata_dir, lang_file)

        # Создаем директорию если не существует
        os.makedirs(tessdata_dir, exist_ok=True)

        with open(lang_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)

        print(f"Языковой файл {lang_file} успешно скачан в {lang_path}")
        return True

    except Exception as e:
        print(f"Ошибка при скачивании языкового файла {lang}: {e}")
        return False


def download_legacy_language_file(lang, tessdata_dir):
    """Скачать legacy файл языка для Tesseract"""
    try:
        # URL для legacy файлов (поддерживает OEM 0-2)
        base_url = "https://github.com/tesseract-ocr/tessdata_best/raw/main"
        lang_file = f"{lang}.traineddata"
        url = f"{base_url}/{lang_file}"

        print(f"Скачивание legacy языкового файла {lang_file}...")

        response = requests.get(url, stream=True)
        response.raise_for_status()

        lang_path = os.path.join(tessdata_dir, lang_file)

        # Создаем резервную копию если файл существует
        if os.path.exists(lang_path):
            backup_path = f"{lang_path}.backup"
            shutil.copy2(lang_path, backup_path)
            print(f"Создана резервная копия: {backup_path}")

        with open(lang_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)

        print(f"Legacy языковой файл {lang_file} успешно скачан в {lang_path}")
        return True

    except Exception as e:
        print(f"Ошибка при скачивании legacy языкового файла {lang}: {e}")
        return False


def check_and_install_language(lang):
    """Проверить и установить языковой файл если необходимо"""
    tessdata_dir = get_tessdata_dir()
    lang_file = os.path.join(tessdata_dir, f'{lang}.traineddata')

    # Проверяем существует ли файл
    if not os.path.isfile(lang_file):
        print(f"Языковой файл {lang}.traineddata не найден. Попытка скачивания...")
        if not download_legacy_language_file(lang, tessdata_dir):
            # Если legacy не получилось, пробуем обычный
            if not download_language_file(lang, tessdata_dir):
                raise FileNotFoundError(f"Не удалось скачать языковой файл для {lang}")
    else:
        # Файл существует, но проверим работает ли он с нужным OEM
        print(f"Языковой файл {lang}.traineddata найден в {lang_file}")

        # Тестируем работу с разными OEM режимами
        test_successful = test_tesseract_config(lang)
        if not test_successful:
            print(f"Существующий файл {lang}.traineddata не поддерживает нужные режимы. Обновление...")
            if not download_legacy_language_file(lang, tessdata_dir):
                print("Не удалось обновить до legacy версии, используем существующий файл")


def test_tesseract_config(lang):
    """Тестировать работу Tesseract с определенным языком и конфигурацией"""
    try:
        # Создаем тестовое изображение
        import PIL.Image
        test_img = PIL.Image.new('RGB', (100, 50), color='white')

        # Тестируем с разными OEM режимами
        configs_to_test = [
            '--oem 3 --psm 13',  # LSTM только
            '--oem 1 --psm 13',  # Legacy + LSTM
            '--oem 0 --psm 13',  # Legacy только
        ]

        for config in configs_to_test:
            try:
                pytesseract.image_to_string(test_img, lang=lang, config=config)
                print(f"Конфигурация '{config}' работает с языком {lang}")
                return True
            except Exception as e:
                print(f"Конфигурация '{config}' не работает: {e}")
                continue

        return False
    except Exception as e:
        print(f"Ошибка при тестировании конфигурации: {e}")
        return False


def check_tesseract_installation():
    """Проверить установку Tesseract"""
    try:
        version = subprocess.check_output(['tesseract', '--version'],
                                          stderr=subprocess.STDOUT).decode('utf-8')
        print(f"Tesseract найден. Версия: {version.split()[1]}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"Tesseract не найден или не установлен: {e}")
        print("Пожалуйста, установите Tesseract:")
        print("Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("Linux: sudo apt install tesseract-ocr")
        print("macOS: brew install tesseract")
        return False


def install_python_packages():
    """Установить необходимые Python пакеты"""
    required_packages = [
        'pytesseract',
        'pdf2image',
        'opencv-python',
        'Pillow',
        'requests'
    ]

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            print(f"Установка пакета {package}...")
            try:
                subprocess.check_call([
                    'pip', 'install', package
                ])
                print(f"Пакет {package} успешно установлен")
            except subprocess.CalledProcessError as e:
                print(f"Ошибка при установке пакета {package}: {e}")


def check_tesseract_version():
    """Проверить версию Tesseract"""
    try:
        version = subprocess.check_output(['tesseract', '--version']).decode('utf-8')
        print(f"Tesseract version: {version}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to get Tesseract version: {e}")
        return False


def preprocess_image(image):
    """Предобработка изображения для улучшения OCR"""
    gray = cv2.cvtColor(np.array(image), cv2.COLOR_BGR2GRAY)

    # Применяем различные фильтры для улучшения качества
    # Убираем шум
    denoised = cv2.medianBlur(gray, 5)

    # Применяем адаптивную бинаризацию
    thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY, 11, 2)

    # Альтернативно - OTSU бинаризация
    # thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)[1]

    return thresh


def apply_markdown(text):
    """Применить разметку Markdown к тексту"""
    lines = text.split('\n')
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        if re.match(r'^\d+\.', line):
            lines[i] = f"{line}"
        elif re.match(r'^[A-Za-zА-Яа-я]+\.', line):
            lines[i] = f"- {line}"
        elif re.match(r'^[A-ZА-Я][a-zа-я]+$', line):
            lines[i] = f"## {line}"
    return '\n'.join(lines)


def pdf_to_text(pdf_path, output_txt_path, language='ukr'):
    """Основная функция для конвертации PDF в текст"""
    try:
        print("=== Проверка системы ===")

        # Проверяем установку Tesseract
        if not check_tesseract_installation():
            return False

        # Проверяем версию Tesseract
        if not check_tesseract_version():
            return False

        # Устанавливаем Python пакеты если нужно
        print("Проверка Python пакетов...")
        install_python_packages()

        # Проверяем и устанавливаем языковые файлы
        print(f"Проверка языкового файла для '{language}'...")
        check_and_install_language(language)

        print("=== Начало обработки PDF ===")

        # Проверяем существование PDF файла
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF файл не найден: {pdf_path}")

        # Конвертируем PDF в изображения
        print("Конвертация PDF в изображения...")
        images = convert_from_path(pdf_path)
        print(f"Найдено страниц: {len(images)}")

        full_text = []

        # Различные конфигурации для попытки
        configs = [
            '--oem 3 --psm 11',  # LSTM движок, блок текста
        ]

        for i, image in enumerate(images):
            print(f"Обработка страницы {i + 1}/{len(images)}...")

            # Предобработка изображения
            processed_image = preprocess_image(image)

            # Пробуем разные конфигурации
            text = None
            for config in configs:
                try:
                    text = pytesseract.image_to_string(processed_image, lang=language, config=config)
                    if text.strip():  # Если получили непустой текст
                        print(f"Успешно обработано с конфигурацией: {config}")
                        break
                except Exception as e:
                    print(f"Конфигурация {config} не сработала: {e}")
                    continue

            if text is None or not text.strip():
                print(f"Не удалось извлечь текст со страницы {i + 1}")
                text = f"[Ошибка извлечения текста со страницы {i + 1}]"

            # Применяем форматирование
            formatted_text = apply_markdown(text)
            full_text.append(f"# Страница {i + 1}\n\n{formatted_text}\n\n---\n\n")

        # Сохраняем результат
        print(f"Сохранение результата в {output_txt_path}...")
        with open(output_txt_path, 'w', encoding='utf-8') as file:
            file.writelines(full_text)

        print(f"✅ Обработка завершена успешно! Результат сохранен в {output_txt_path}")
        return True

    except Exception as e:
        print(f"❌ Произошла ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Главная функция"""
    # Настройки
    pdf_path = r"Test VN & TTN.pdf"  # Замените на путь к вашему PDF файлу
    output_txt_path = 'output_text.md'  # Путь к выходному файлу
    language = 'ukr'  # Язык для OCR

    # Запуск обработки
    success = pdf_to_text(pdf_path, output_txt_path, language)

    if success:
        print(f"\n🎉 Файл успешно обработан!")
        print(f"📄 Исходный файл: {pdf_path}")
        print(f"📝 Результат: {output_txt_path}")
    else:
        print(f"\n❌ Обработка файла завершилась с ошибками")


if __name__ == "__main__":
    main()