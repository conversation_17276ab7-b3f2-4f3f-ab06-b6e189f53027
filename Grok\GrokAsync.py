# pip install httpx python-dotenv
import base64
import os
import httpx
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
import json
import asyncio
import sys
from pathlib import Path
import random
import time

parrent_dir = str(Path(__file__).resolve().parent.parent)
sys.path.append(parrent_dir)
from prompt import PROMPT_EXAMPLE_GEMINI

# Загрузка переменных окружения из файла .env
load_dotenv()

# Получаем API ключ из переменной окружения GROK_API_KEY
api_key = os.getenv("GROK_API_KEY")
if not api_key:
    print("Ошибка: Не найден API ключ GROK_API_KEY в файле .env")
    exit(1)


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")
            return {}
    return json_string if isinstance(json_string, dict) else {}


async def make_api_request_with_retry(
        client: httpx.AsyncClient,
        request_data: Dict[str, Any],
        headers: Dict[str, str],
        max_retries: int = 3,
        base_delay: float = 1.0
) -> Dict[str, Any]:
    """
    Выполняет API запрос с механизмом повторных попыток и экспоненциальной задержкой

    Args:
        client: HTTP клиент
        request_data: Данные для запроса
        headers: Заголовки запроса
        max_retries: Максимальное количество повторных попыток (по умолчанию 3)
        base_delay: Базовая задержка в секундах (по умолчанию 1.0)

    Returns:
        Словарь с результатом или пустой словарь в случае ошибки
    """

    for attempt in range(max_retries + 1):  # +1 для первоначальной попытки
        try:
            print(f"Попытка {attempt + 1}/{max_retries + 1}...")

            response = await client.post(
                "https://api.x.ai/v1/chat/completions",
                json=request_data,
                headers=headers
            )
            response.raise_for_status()  # Проверяем статус ответа

            response_json = response.json()
            response_text = response_json["choices"][0]["message"]["content"]
            reasoning_content = response_json["choices"][0]["message"]['reasoning_content']  # рассуждения
            tokens = response_json['usage']  # все токены
            # print(tokens)
            clear_result = clear_text(response_text)

            print(f"✅ Запрос успешно выполнен с попытки {attempt + 1}")
            return clear_result

        except httpx.HTTPStatusError as e:
            error_details = f"HTTP {e.response.status_code}: {e.response.text}"
            print(f"❌ HTTP ошибка на попытке {attempt + 1}: {error_details}")

            # Если это последняя попытка, возвращаем пустой результат
            if attempt == max_retries:
                print(f"🚫 Все попытки исчерпаны. Финальная ошибка: {error_details}")
                return {}

            # Вычисляем задержку с экспоненциальным ростом и добавлением случайности (jitter)
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"⏳ Ожидание {delay:.2f} секунд перед следующей попыткой...")
            await asyncio.sleep(delay)

        except httpx.RequestError as e:
            error_details = f"Ошибка запроса: {e}"
            print(f"❌ Ошибка сети на попытке {attempt + 1}: {error_details}")

            if attempt == max_retries:
                print(f"🚫 Все попытки исчерпаны. Финальная ошибка: {error_details}")
                return {}

            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"⏳ Ожидание {delay:.2f} секунд перед следующей попыткой...")
            await asyncio.sleep(delay)

        except Exception as e:
            error_details = f"Неожиданная ошибка: {e}"
            print(f"❌ Неожиданная ошибка на попытке {attempt + 1}: {error_details}")

            if attempt == max_retries:
                print(f"🚫 Все попытки исчерпаны. Финальная ошибка: {error_details}")
                return {}

            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"⏳ Ожидание {delay:.2f} секунд перед следующей попыткой...")
            await asyncio.sleep(delay)


async def extract_data_by_grok_async(
        content: str,
        prompt=PROMPT_EXAMPLE_GEMINI,
        max_retries: int = 3,
        base_delay: float = 1.0,
        timeout: float = 120.0
) -> Dict[str, Any]:
    """
    Извлекает данные через GROK API с механизмом повторных попыток

    Args:
        content: Контент для обработки
        prompt: Промпт для модели
        max_retries: Максимальное количество повторных попыток
        base_delay: Базовая задержка для экспоненциального роста
        timeout: Таймаут для HTTP клиента в секундах
    """
    # Подготавливаем сообщения для API
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
    ]

    # Подготавливаем данные для запроса
    request_data = {
        "model": "grok-3-mini",
        "reasoning_effort": "high",
        "messages": messages,
        "temperature": 0.1,
        "max_completion_tokens": 65000,
        "top_p": 0.1,
        "stream": False,
        "response_format": {"type": "json_object"},
        "stop": None,
    }

    # Заголовки для запроса
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Создаем асинхронный HTTP-клиент и выполняем запрос с повторными попытками
    async with httpx.AsyncClient(timeout=timeout) as client:
        result = await make_api_request_with_retry(
            client=client,
            request_data=request_data,
            headers=headers,
            max_retries=max_retries,
            base_delay=base_delay
        )
        return result


async def process_multiple_contents(
        contents: List[str],
        prompt=PROMPT_EXAMPLE_GEMINI,
        max_retries: int = 3,
        base_delay: float = 1.0
) -> List[Dict[str, Any]]:
    """
    Асинхронная обработка нескольких контентов параллельно с повторными попытками
    """
    print(f"🚀 Начинаем параллельную обработку {len(contents)} контентов...")

    tasks = [
        extract_data_by_grok_async(content, prompt, max_retries, base_delay)
        for content in contents
    ]

    start_time = time.time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = time.time()

    print(f"⏱️ Общее время обработки: {end_time - start_time:.2f} секунд")

    # Обрабатываем результаты и исключения
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"❌ Критическая ошибка при обработке контента {i}: {result}")
            processed_results.append({})
        else:
            processed_results.append(result)

    return processed_results


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    content = """
Eyeball Marshmallow 4gx100pcsx12boxes'Eyeball Marshmallow 4gx100pcsx12boxes',
Jelly cup in Elephant Jar  13gx100pcsx6jars'Jelly cup in Elephant Jar  13gx100pcsx6jars',
Jelly cup in bear Jar  13gx100pcsx6jars'Jelly cup in bear Jar  13gx100pcsx6jars',
    """

    prompt = """Тебе дан список артикулов. Ты - аналитик, глубоко разбирающийся в ассортименте. 
                Однозначно определяешь где начинается и заканчивается каждый артикул.
                Максимально точно определи и извлеки данные по каждому артикулу в формате ВАЛИДНОГО JSON.                 
                Ты не можешь определить сколько артикулов? Почему извлекаешь данные только для первого артикула?
                Используй только данные из предложенных вариантов.
                {articles:[{
                    "sku": str,  // наименование, которое тебе дал. Без изменений
                    "grams_in_pcs": float,  // grams, ml
                    "pcs_in_block": float,
                    "box_in_cartoon": int,
                    "weight_unit": str, // g, ml, kg, гр, кг, мл
                    "pcs_type": str, // pcs, шт
                    "box_type": str // jar, box, банка, блок
                }]}
            """

    # Пример обработки одного контента
    result = asyncio.run(extract_data_by_grok_async(content, prompt))
    print("Результат одного запроса:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

    # Пример параллельной обработки нескольких контентов
    contents = [
        content,
        "Another product data here...",
        "Yet another product data..."
    ]

    print("\nРезультаты параллельной обработки:")
    results = asyncio.run(process_multiple_contents(contents, prompt))
    for i, result in enumerate(results):
        print(f"Результат {i + 1}:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        print()
