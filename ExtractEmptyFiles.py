# Разделяет страницы PDF на два файла: с данными и пустые, сохраняя их в отдельные файлы.

import fitz
import numpy as np
from datetime import datetime
import os
import asyncio


async def is_blank(page, threshold=0.99, white_level=250):
    """
    Проверяет, является ли страница пустой, анализируя содержимое пикселей.

    :param page: Объект страницы PyMuPDF.
    :param threshold: Доля пикселей, которые должны быть "белыми", чтобы страница считалась пустой (по умолчанию: 0.99).
    :param white_level: Значение пикселя, выше которого пиксель считается "белым" (по умолчанию: 250 из 255).
    :return: True, если страница пустая, False в противном случае.
    """
    pix = page.get_pixmap(matrix=fitz.Identity, colorspace=fitz.csGRAY)
    pixels = np.frombuffer(pix.samples, dtype=np.uint8)
    total = pixels.size
    blank_count = np.sum(pixels > white_level)
    return blank_count / total > threshold


async def separate_pages_by_content(input_path, threshold=0.99, white_level=250):
    """
    Разделяет страницы PDF на две категории: с данными и пустые, сохраняя их в отдельные файлы.
    """
    doc = fitz.open(input_path)
    data_doc = fitz.open()
    empty_doc = fitz.open()

    for pgno in range(len(doc)):
        page = doc.load_page(pgno)
        if await is_blank(page, threshold, white_level):
            empty_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)
        else:
            data_doc.insert_pdf(doc, from_page=pgno, to_page=pgno)

    base_name = os.path.splitext(os.path.basename(input_path))[0]
    input_dir = os.path.dirname(input_path)
    if not input_dir:
        input_dir = "."

    data_output_path = os.path.join(input_dir, f"{base_name}-data.pdf")
    empty_output_path = os.path.join(input_dir, f"{base_name}-empty.pdf")

    data_doc.save(data_output_path)
    empty_doc.save(empty_output_path)

    doc.close()
    data_doc.close()
    empty_doc.close()

    return data_output_path, empty_output_path


async def divide_documents(input_path):
    """
    Извлекает каждый документ из PDF в отдельный файл.
    """
    data_path, empty_path = await separate_pages_by_content(input_path)
    directory = os.path.dirname(data_path)


if __name__ == "__main__":
    # Пример использования
    file_path = r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\Scan.pdf"
    asyncio.run(divide_documents(file_path))
